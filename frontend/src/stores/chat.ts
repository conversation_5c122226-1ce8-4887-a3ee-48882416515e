import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { chatAPI, streamingService, type ChatMessage } from '@/services/api';

export interface Session {
  id: string;
  title: string;
  messageCount: number;
  lastActiveTime: Date;
  createdAt: Date;
  messages: ChatMessage[];
}

export const useChatStore = defineStore('chat', () => {
  // 状态
  const messages = ref<ChatMessage[]>([]);
  const currentThreadId = ref<string>('');
  const isLoading = ref(false);
  const isConnected = ref(false);
  const websocket = ref<WebSocket | null>(null);

  // 会话管理状态
  const sessions = ref<Session[]>([]);
  const currentSessionId = ref<string>('');

  // 计算属性
  const hasMessages = computed(() => messages.value.length > 0);
  const lastMessage = computed(() => 
    messages.value[messages.value.length - 1]
  );

  // 方法
  const generateThreadId = () => {
    return `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const addMessage = (message: ChatMessage) => {
    messages.value.push({
      ...message,
      timestamp: message.timestamp || new Date().toISOString(),
    });

    // 更新当前会话信息
    updateCurrentSessionTitle();
    if (currentSessionId.value) {
      const session = sessions.value.find(s => s.id === currentSessionId.value);
      if (session) {
        session.messageCount = messages.value.length;
        session.lastActiveTime = new Date();
      }
    }
  };

  const sendMessage = async (content: string, useStreaming: boolean = true) => {
    if (!content.trim()) return;

    // 确保有线程ID
    if (!currentThreadId.value) {
      currentThreadId.value = generateThreadId();
    }

    // 添加用户消息
    addMessage({
      role: 'user',
      content: content.trim(),
    });

    isLoading.value = true;

    if (useStreaming) {
      // 使用流式响应
      await sendStreamingMessage(content.trim());
    } else {
      // 使用传统方式
      await sendRegularMessage(content.trim());
    }
  };

  const sendStreamingMessage = async (content: string) => {
    // 添加一个占位的AI消息
    const aiMessageIndex = messages.value.length;
    addMessage({
      role: 'assistant',
      content: '',
    });

    try {
      await streamingService.createStreamingChat(
        {
          message: content,
          thread_id: currentThreadId.value,
          stream: true,
        },
        {
          onStart: () => {
            console.log('开始流式响应');
          },
          onChunk: (content: string, isComplete: boolean) => {
            // 更新AI消息内容
            if (messages.value[aiMessageIndex]) {
              messages.value[aiMessageIndex].content = content;
            }
          },
          onComplete: (fullResponse: string) => {
            console.log('流式响应完成:', fullResponse);
            isLoading.value = false;
          },
          onError: (error: Error) => {
            console.error('流式响应错误:', error);
            if (messages.value[aiMessageIndex]) {
              messages.value[aiMessageIndex].content = '抱歉，处理消息时出现错误，请重试。';
            }
            isLoading.value = false;
          }
        }
      );
    } catch (error) {
      console.error('发送流式消息失败:', error);
      if (messages.value[aiMessageIndex]) {
        messages.value[aiMessageIndex].content = '抱歉，发送消息时出现错误，请重试。';
      }
      isLoading.value = false;
    }
  };

  const sendRegularMessage = async (content: string) => {
    try {
      // 发送到后端
      const response = await chatAPI.sendMessage({
        message: content,
        thread_id: currentThreadId.value,
      });

      // 添加AI回复
      addMessage({
        role: 'assistant',
        content: response.message,
      });

    } catch (error) {
      console.error('发送消息失败:', error);
      addMessage({
        role: 'assistant',
        content: '抱歉，发送消息时出现错误，请重试。',
      });
    } finally {
      isLoading.value = false;
    }
  };

  const connectWebSocket = () => {
    if (!currentThreadId.value) {
      currentThreadId.value = generateThreadId();
    }

    try {
      websocket.value = chatAPI.connectWebSocket(currentThreadId.value);
      
      websocket.value.onopen = () => {
        isConnected.value = true;
        console.log('WebSocket连接已建立');
      };

      websocket.value.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'ai_chunk') {
          // 处理流式响应
          const lastMsg = messages.value[messages.value.length - 1];
          if (lastMsg && lastMsg.role === 'assistant' && lastMsg.content === '正在思考中...') {
            lastMsg.content = data.chunk.response || data.chunk;
          } else {
            addMessage({
              role: 'assistant',
              content: data.chunk.response || data.chunk,
            });
          }
        }
      };

      websocket.value.onclose = () => {
        isConnected.value = false;
        console.log('WebSocket连接已关闭');
      };

      websocket.value.onerror = (error) => {
        console.error('WebSocket错误:', error);
        isConnected.value = false;
      };

    } catch (error) {
      console.error('WebSocket连接失败:', error);
    }
  };

  const disconnectWebSocket = () => {
    if (websocket.value) {
      websocket.value.close();
      websocket.value = null;
      isConnected.value = false;
    }
  };

  const clearMessages = () => {
    messages.value = [];
    currentThreadId.value = '';
  };

  const loadSessionHistory = async (threadId: string) => {
    try {
      const history = await chatAPI.getSessionHistory(threadId);
      messages.value = history;
      currentThreadId.value = threadId;
    } catch (error) {
      console.error('加载会话历史失败:', error);
    }
  };

  // 会话管理方法
  const createNewSession = (): string => {
    const sessionId = generateThreadId();
    const newSession: Session = {
      id: sessionId,
      title: '新对话',
      messageCount: 0,
      lastActiveTime: new Date(),
      createdAt: new Date(),
      messages: []
    };

    sessions.value.unshift(newSession);
    switchToSession(sessionId);
    return sessionId;
  };

  const switchToSession = (sessionId: string) => {
    // 保存当前会话的消息
    if (currentSessionId.value) {
      const currentSession = sessions.value.find(s => s.id === currentSessionId.value);
      if (currentSession) {
        currentSession.messages = [...messages.value];
        currentSession.messageCount = messages.value.length;
        currentSession.lastActiveTime = new Date();
      }
    }

    // 切换到新会话
    const targetSession = sessions.value.find(s => s.id === sessionId);
    if (targetSession) {
      currentSessionId.value = sessionId;
      currentThreadId.value = sessionId;
      messages.value = [...targetSession.messages];

      // 更新最后活跃时间
      targetSession.lastActiveTime = new Date();
    }
  };

  const deleteSession = (sessionId: string) => {
    const sessionIndex = sessions.value.findIndex(s => s.id === sessionId);
    if (sessionIndex === -1) return;

    sessions.value.splice(sessionIndex, 1);

    // 如果删除的是当前会话，切换到其他会话或创建新会话
    if (currentSessionId.value === sessionId) {
      if (sessions.value.length > 0) {
        switchToSession(sessions.value[0].id);
      } else {
        createNewSession();
      }
    }
  };

  const renameSession = (sessionId: string, newTitle: string) => {
    const session = sessions.value.find(s => s.id === sessionId);
    if (session) {
      session.title = newTitle;
    }
  };

  const updateCurrentSessionTitle = () => {
    if (currentSessionId.value && messages.value.length > 0) {
      const session = sessions.value.find(s => s.id === currentSessionId.value);
      if (session && session.title === '新对话') {
        // 使用第一条用户消息作为标题
        const firstUserMessage = messages.value.find(m => m.role === 'user');
        if (firstUserMessage) {
          const title = firstUserMessage.content.slice(0, 20);
          session.title = title.length < firstUserMessage.content.length ? title + '...' : title;
        }
      }
    }
  };

  return {
    // 状态
    messages,
    currentThreadId,
    isLoading,
    isConnected,
    
    // 计算属性
    hasMessages,
    lastMessage,
    
    // 方法
    addMessage,
    sendMessage,
    connectWebSocket,
    disconnectWebSocket,
    clearMessages,
    loadSessionHistory,
  };
});
