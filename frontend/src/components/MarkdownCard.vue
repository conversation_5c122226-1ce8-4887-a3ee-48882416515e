<template>
  <div class="markdown-card" :class="themeClass">
    <div class="markdown-content" v-html="renderedContent"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';

interface Props {
  content: string;
  theme?: 'light' | 'dark';
  typing?: boolean;
  typingSpeed?: number;
  enableCodeCopy?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  theme: 'light',
  typing: false,
  typingSpeed: 30,
  enableCodeCopy: true
});

const emit = defineEmits<{
  'typing-complete': [];
}>();

// 响应式数据
const displayContent = ref('');
const isTyping = ref(false);
const typingTimer = ref<number | null>(null);

// Markdown渲染器配置
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  breaks: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        const highlighted = hljs.highlight(str, { language: lang }).value;
        return `<pre class="hljs"><code class="hljs language-${lang}">${highlighted}</code></pre>`;
      } catch (__) {}
    }
    return `<pre class="hljs"><code class="hljs">${md.utils.escapeHtml(str)}</code></pre>`;
  }
});

// 自定义渲染规则
md.renderer.rules.code_block = (tokens, idx) => {
  const token = tokens[idx];
  const lang = token.info ? token.info.trim() : '';
  const langClass = lang ? ` language-${lang}` : '';
  const copyButton = props.enableCodeCopy ? 
    `<button class="code-copy-btn" onclick="copyCode(this)" title="复制代码">📋</button>` : '';
  
  let highlighted = token.content;
  if (lang && hljs.getLanguage(lang)) {
    try {
      highlighted = hljs.highlight(token.content, { language: lang }).value;
    } catch (__) {}
  } else {
    highlighted = md.utils.escapeHtml(token.content);
  }

  return `<div class="code-block">
    <div class="code-header">
      <span class="code-lang">${lang || 'text'}</span>
      ${copyButton}
    </div>
    <pre class="hljs"><code class="hljs${langClass}">${highlighted}</code></pre>
  </div>`;
};

// 自定义表格渲染
md.renderer.rules.table_open = () => '<div class="table-wrapper"><table class="markdown-table">';
md.renderer.rules.table_close = () => '</table></div>';

// 计算属性
const themeClass = computed(() => {
  return props.theme === 'dark' ? 'markdown-card-dark' : 'markdown-card-light';
});

const renderedContent = computed(() => {
  if (!displayContent.value) return '';
  
  let content = md.render(displayContent.value);
  
  // 添加代码复制功能的脚本
  if (props.enableCodeCopy) {
    content += `
      <script>
        window.copyCode = function(button) {
          const codeBlock = button.parentElement.nextElementSibling.querySelector('code');
          const text = codeBlock.textContent || codeBlock.innerText;
          navigator.clipboard.writeText(text).then(() => {
            const originalText = button.textContent;
            button.textContent = '✅';
            setTimeout(() => {
              button.textContent = originalText;
            }, 2000);
          }).catch(err => {
            console.error('复制失败:', err);
          });
        };
      </script>
    `;
  }
  
  return content;
});

// 打字机效果
const startTyping = () => {
  if (!props.typing || !props.content) {
    displayContent.value = props.content;
    return;
  }

  isTyping.value = true;
  displayContent.value = '';
  let currentIndex = 0;

  const typeNextChar = () => {
    if (currentIndex < props.content.length) {
      displayContent.value = props.content.slice(0, currentIndex + 1);
      currentIndex++;
      typingTimer.value = window.setTimeout(typeNextChar, props.typingSpeed);
    } else {
      isTyping.value = false;
      emit('typing-complete');
    }
  };

  typeNextChar();
};

const stopTyping = () => {
  if (typingTimer.value) {
    clearTimeout(typingTimer.value);
    typingTimer.value = null;
  }
  isTyping.value = false;
  displayContent.value = props.content;
};

// 监听内容变化
watch(() => props.content, (newContent, oldContent) => {
  if (newContent !== oldContent) {
    if (typingTimer.value) {
      clearTimeout(typingTimer.value);
    }
    startTyping();
  }
}, { immediate: true });

// 监听打字机设置变化
watch(() => props.typing, (newTyping) => {
  if (!newTyping) {
    stopTyping();
  } else {
    startTyping();
  }
});

// 生命周期
onMounted(() => {
  startTyping();
});

// 暴露方法
defineExpose({
  startTyping,
  stopTyping,
  isTyping: () => isTyping.value
});
</script>

<style scoped>
.markdown-card {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
  word-wrap: break-word;
}

.markdown-card-light {
  color: #333;
}

.markdown-card-dark {
  color: #e6e6e6;
}

.markdown-content {
  max-width: 100%;
  overflow-x: auto;
}

/* 全局样式 */
.markdown-card :deep(h1),
.markdown-card :deep(h2),
.markdown-card :deep(h3),
.markdown-card :deep(h4),
.markdown-card :deep(h5),
.markdown-card :deep(h6) {
  margin: 1.5em 0 0.5em 0;
  font-weight: 600;
  line-height: 1.3;
}

.markdown-card :deep(h1) { font-size: 2em; }
.markdown-card :deep(h2) { font-size: 1.5em; }
.markdown-card :deep(h3) { font-size: 1.25em; }

.markdown-card :deep(p) {
  margin: 0.8em 0;
  line-height: 1.7;
}

.markdown-card :deep(ul),
.markdown-card :deep(ol) {
  margin: 0.8em 0;
  padding-left: 2em;
}

.markdown-card :deep(li) {
  margin: 0.3em 0;
}

.markdown-card :deep(blockquote) {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #ddd;
  background: #f9f9f9;
  color: #666;
}

.markdown-card-dark :deep(blockquote) {
  background: #2a2a2a;
  border-left-color: #555;
  color: #ccc;
}

.markdown-card :deep(code) {
  background: #f1f1f1;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.markdown-card-dark :deep(code) {
  background: #2a2a2a;
  color: #e6e6e6;
}

.markdown-card :deep(.code-block) {
  margin: 1em 0;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #e1e4e8;
}

.markdown-card-dark :deep(.code-block) {
  border-color: #30363d;
}

.markdown-card :deep(.code-header) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f6f8fa;
  border-bottom: 1px solid #e1e4e8;
  font-size: 12px;
}

.markdown-card-dark :deep(.code-header) {
  background: #161b22;
  border-bottom-color: #30363d;
  color: #e6e6e6;
}

.markdown-card :deep(.code-lang) {
  font-weight: 600;
  color: #586069;
}

.markdown-card-dark :deep(.code-lang) {
  color: #8b949e;
}

.markdown-card :deep(.code-copy-btn) {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 12px;
  transition: background-color 0.2s;
}

.markdown-card :deep(.code-copy-btn:hover) {
  background: #e1e4e8;
}

.markdown-card-dark :deep(.code-copy-btn:hover) {
  background: #30363d;
}

.markdown-card :deep(.hljs) {
  background: #f6f8fa !important;
  padding: 12px;
  margin: 0;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.45;
}

.markdown-card-dark :deep(.hljs) {
  background: #0d1117 !important;
  color: #e6edf3;
}

.markdown-card :deep(.table-wrapper) {
  overflow-x: auto;
  margin: 1em 0;
}

.markdown-card :deep(.markdown-table) {
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #e1e4e8;
}

.markdown-card-dark :deep(.markdown-table) {
  border-color: #30363d;
}

.markdown-card :deep(.markdown-table th),
.markdown-card :deep(.markdown-table td) {
  padding: 8px 12px;
  border: 1px solid #e1e4e8;
  text-align: left;
}

.markdown-card-dark :deep(.markdown-table th),
.markdown-card-dark :deep(.markdown-table td) {
  border-color: #30363d;
}

.markdown-card :deep(.markdown-table th) {
  background: #f6f8fa;
  font-weight: 600;
}

.markdown-card-dark :deep(.markdown-table th) {
  background: #161b22;
}

.markdown-card :deep(a) {
  color: #0366d6;
  text-decoration: none;
}

.markdown-card :deep(a:hover) {
  text-decoration: underline;
}

.markdown-card-dark :deep(a) {
  color: #58a6ff;
}

.markdown-card :deep(hr) {
  border: none;
  border-top: 1px solid #e1e4e8;
  margin: 2em 0;
}

.markdown-card-dark :deep(hr) {
  border-top-color: #30363d;
}
</style>
