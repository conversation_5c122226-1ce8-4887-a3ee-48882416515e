<template>
  <div class="file-upload-container">
    <!-- 文件拖拽区域 -->
    <div 
      class="file-drop-zone"
      :class="{ 
        'drag-over': isDragOver,
        'uploading': isUploading 
      }"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
    >
      <div class="drop-zone-content">
        <div class="upload-icon">
          <span v-if="!isUploading">📁</span>
          <span v-else class="loading-spinner">⏳</span>
        </div>
        <div class="upload-text">
          <p v-if="!isUploading" class="primary-text">
            点击或拖拽文件到此处上传
          </p>
          <p v-else class="primary-text">
            正在上传文件...
          </p>
          <p class="secondary-text">
            支持图片、文档、代码等多种格式，最大50MB
          </p>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      multiple
      :accept="acceptedTypes"
      @change="handleFileSelect"
      style="display: none"
    />

    <!-- 上传进度 -->
    <div v-if="uploadProgress.length > 0" class="upload-progress">
      <div 
        v-for="progress in uploadProgress" 
        :key="progress.id"
        class="progress-item"
      >
        <div class="progress-info">
          <span class="filename">{{ progress.filename }}</span>
          <span class="status" :class="progress.status">
            {{ getStatusText(progress.status) }}
          </span>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${progress.progress}%` }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 已上传文件列表 -->
    <div v-if="uploadedFiles.length > 0" class="uploaded-files">
      <h4>已上传文件</h4>
      <div class="file-list">
        <div 
          v-for="file in uploadedFiles" 
          :key="file.file_id"
          class="file-item"
        >
          <div class="file-icon">
            {{ getFileIcon(file.file_type) }}
          </div>
          <div class="file-info">
            <div class="file-name">{{ file.filename }}</div>
            <div class="file-meta">
              {{ formatFileSize(file.file_size) }} • {{ file.file_type }}
            </div>
          </div>
          <div class="file-actions">
            <button 
              @click="previewFile(file)"
              class="action-btn preview-btn"
              title="预览"
            >
              👁️
            </button>
            <button 
              @click="downloadFile(file)"
              class="action-btn download-btn"
              title="下载"
            >
              ⬇️
            </button>
            <button 
              @click="removeFile(file.file_id)"
              class="action-btn delete-btn"
              title="删除"
            >
              🗑️
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览 -->
    <FilePreview
      :visible="showPreview"
      :file="previewFile"
      @close="closePreview"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { chatAPI, type FileUploadResponse, type FileInfo } from '@/services/api';
import FilePreview from './FilePreview.vue';

interface Props {
  multiple?: boolean;
  maxSize?: number; // MB
  acceptedTypes?: string;
}

const props = withDefaults(defineProps<Props>(), {
  multiple: true,
  maxSize: 50,
  acceptedTypes: '*'
});

const emit = defineEmits<{
  'file-uploaded': [file: FileUploadResponse];
  'files-uploaded': [files: FileUploadResponse[]];
  'file-removed': [fileId: string];
}>();

// 响应式数据
const fileInput = ref<HTMLInputElement>();
const isDragOver = ref(false);
const isUploading = ref(false);
const uploadProgress = ref<Array<{
  id: string;
  filename: string;
  progress: number;
  status: 'uploading' | 'success' | 'error';
}>>([]);
const uploadedFiles = ref<FileUploadResponse[]>([]);
const showPreview = ref(false);
const previewFile = ref<FileInfo | null>(null);

// 计算属性
const maxSizeBytes = computed(() => props.maxSize * 1024 * 1024);

// 方法
const triggerFileInput = () => {
  if (!isUploading.value) {
    fileInput.value?.click();
  }
};

const handleDragOver = (e: DragEvent) => {
  e.preventDefault();
  isDragOver.value = true;
};

const handleDragEnter = (e: DragEvent) => {
  e.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault();
  isDragOver.value = false;
};

const handleDrop = (e: DragEvent) => {
  e.preventDefault();
  isDragOver.value = false;
  
  const files = Array.from(e.dataTransfer?.files || []);
  if (files.length > 0) {
    uploadFiles(files);
  }
};

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const files = Array.from(target.files || []);
  if (files.length > 0) {
    uploadFiles(files);
  }
  // 清空input值，允许重复选择同一文件
  target.value = '';
};

const validateFile = (file: File): string | null => {
  if (file.size > maxSizeBytes.value) {
    return `文件大小超过限制 (${props.maxSize}MB)`;
  }
  return null;
};

const uploadFiles = async (files: File[]) => {
  if (isUploading.value) return;
  
  isUploading.value = true;
  const uploadedResults: FileUploadResponse[] = [];
  
  for (const file of files) {
    const progressId = `${Date.now()}_${Math.random()}`;
    
    // 验证文件
    const error = validateFile(file);
    if (error) {
      uploadProgress.value.push({
        id: progressId,
        filename: file.name,
        progress: 0,
        status: 'error'
      });
      continue;
    }
    
    // 添加进度项
    uploadProgress.value.push({
      id: progressId,
      filename: file.name,
      progress: 0,
      status: 'uploading'
    });
    
    try {
      // 模拟上传进度
      const progressItem = uploadProgress.value.find(p => p.id === progressId);
      if (progressItem) {
        progressItem.progress = 50;
      }
      
      // 上传文件
      const result = await chatAPI.uploadFile(file);
      
      // 更新进度
      if (progressItem) {
        progressItem.progress = 100;
        progressItem.status = 'success';
      }
      
      uploadedResults.push(result);
      uploadedFiles.value.push(result);
      
      emit('file-uploaded', result);
      
    } catch (error) {
      console.error('文件上传失败:', error);
      const progressItem = uploadProgress.value.find(p => p.id === progressId);
      if (progressItem) {
        progressItem.status = 'error';
      }
    }
  }
  
  isUploading.value = false;
  
  if (uploadedResults.length > 0) {
    emit('files-uploaded', uploadedResults);
  }
  
  // 3秒后清除进度显示
  setTimeout(() => {
    uploadProgress.value = [];
  }, 3000);
};

const removeFile = async (fileId: string) => {
  try {
    await chatAPI.deleteFile(fileId);
    uploadedFiles.value = uploadedFiles.value.filter(f => f.file_id !== fileId);
    emit('file-removed', fileId);
  } catch (error) {
    console.error('删除文件失败:', error);
  }
};

const previewFile = async (file: FileUploadResponse) => {
  try {
    // 获取完整的文件信息
    const fileInfo = await chatAPI.getFileInfo(file.file_id);
    previewFile.value = fileInfo;
    showPreview.value = true;
  } catch (error) {
    console.error('获取文件信息失败:', error);
    // 降级到直接下载
    downloadFile(file);
  }
};

const closePreview = () => {
  showPreview.value = false;
  previewFile.value = null;
};

const downloadFile = (file: FileUploadResponse) => {
  const link = document.createElement('a');
  link.href = `http://localhost:8000${file.file_path}`;
  link.download = file.filename;
  link.click();
};

const getFileIcon = (fileType: string): string => {
  const icons: Record<string, string> = {
    image: '🖼️',
    document: '📄',
    spreadsheet: '📊',
    code: '💻',
    archive: '📦',
    audio: '🎵',
    video: '🎬',
    other: '📎'
  };
  return icons[fileType] || icons.other;
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    uploading: '上传中...',
    success: '上传成功',
    error: '上传失败'
  };
  return statusMap[status] || status;
};
</script>

<style scoped>
.file-upload-container {
  width: 100%;
}

.file-drop-zone {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.file-drop-zone:hover,
.file-drop-zone.drag-over {
  border-color: #1890ff;
  background: #f6f9ff;
}

.file-drop-zone.uploading {
  border-color: #52c41a;
  background: #f6ffed;
  cursor: not-allowed;
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-icon {
  font-size: 48px;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.upload-text .primary-text {
  font-size: 16px;
  color: #333;
  margin: 0 0 8px 0;
}

.upload-text .secondary-text {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.upload-progress {
  margin-top: 20px;
}

.progress-item {
  margin-bottom: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.filename {
  font-size: 14px;
  color: #333;
}

.status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.status.uploading {
  background: #e6f7ff;
  color: #1890ff;
}

.status.success {
  background: #f6ffed;
  color: #52c41a;
}

.status.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.progress-bar {
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #1890ff;
  transition: width 0.3s ease;
}

.uploaded-files {
  margin-top: 20px;
}

.uploaded-files h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #333;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: white;
}

.file-icon {
  font-size: 24px;
  margin-right: 12px;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #666;
}

.file-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  padding: 4px 8px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  font-size: 16px;
  transition: background 0.2s;
}

.action-btn:hover {
  background: #f0f0f0;
}

.delete-btn:hover {
  background: #ffebee;
}
</style>
