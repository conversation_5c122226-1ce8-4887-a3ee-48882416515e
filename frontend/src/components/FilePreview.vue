<template>
  <div v-if="visible" class="file-preview-overlay" @click="handleOverlayClick">
    <div class="file-preview-modal" @click.stop>
      <!-- 预览头部 -->
      <div class="preview-header">
        <div class="file-info">
          <h3 class="file-name">{{ file.filename }}</h3>
          <div class="file-meta">
            {{ formatFileSize(file.file_size) }} • {{ file.file_type }} • {{ formatDate(file.upload_time) }}
          </div>
        </div>
        <div class="header-actions">
          <button @click="downloadFile" class="action-btn download-btn" title="下载">
            ⬇️ 下载
          </button>
          <button @click="closePreview" class="action-btn close-btn" title="关闭">
            ✕
          </button>
        </div>
      </div>

      <!-- 预览内容 -->
      <div class="preview-content">
        <!-- 图片预览 -->
        <div v-if="file.file_type === 'image'" class="image-preview">
          <img 
            :src="fileUrl" 
            :alt="file.filename"
            @load="onImageLoad"
            @error="onImageError"
            class="preview-image"
          />
        </div>

        <!-- 文本文件预览 -->
        <div v-else-if="isTextFile" class="text-preview">
          <div v-if="textContent" class="text-content">
            <pre><code>{{ textContent }}</code></pre>
          </div>
          <div v-else-if="loadingText" class="loading-state">
            正在加载文件内容...
          </div>
          <div v-else class="error-state">
            无法加载文件内容
          </div>
        </div>

        <!-- PDF预览 -->
        <div v-else-if="file.file_type === 'document' && isPdf" class="pdf-preview">
          <iframe 
            :src="fileUrl" 
            class="pdf-iframe"
            title="PDF预览"
          ></iframe>
        </div>

        <!-- 音频预览 -->
        <div v-else-if="file.file_type === 'audio'" class="audio-preview">
          <audio controls class="audio-player">
            <source :src="fileUrl" :type="getMimeType(file.filename)">
            您的浏览器不支持音频播放。
          </audio>
        </div>

        <!-- 视频预览 -->
        <div v-else-if="file.file_type === 'video'" class="video-preview">
          <video controls class="video-player">
            <source :src="fileUrl" :type="getMimeType(file.filename)">
            您的浏览器不支持视频播放。
          </video>
        </div>

        <!-- 代码文件预览 -->
        <div v-else-if="file.file_type === 'code'" class="code-preview">
          <div v-if="textContent" class="code-content">
            <pre><code :class="getLanguageClass(file.filename)">{{ textContent }}</code></pre>
          </div>
          <div v-else-if="loadingText" class="loading-state">
            正在加载代码内容...
          </div>
          <div v-else class="error-state">
            无法加载代码内容
          </div>
        </div>

        <!-- 不支持预览的文件类型 -->
        <div v-else class="unsupported-preview">
          <div class="unsupported-icon">📄</div>
          <div class="unsupported-text">
            <p>此文件类型不支持预览</p>
            <p class="file-type-info">{{ file.file_type }} 文件</p>
            <button @click="downloadFile" class="download-btn-large">
              ⬇️ 下载文件
            </button>
          </div>
        </div>
      </div>

      <!-- 预览底部操作 -->
      <div class="preview-footer" v-if="file.file_type === 'image'">
        <div class="zoom-controls">
          <button @click="zoomOut" class="zoom-btn" :disabled="zoomLevel <= 0.5">
            🔍-
          </button>
          <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
          <button @click="zoomIn" class="zoom-btn" :disabled="zoomLevel >= 3">
            🔍+
          </button>
          <button @click="resetZoom" class="zoom-btn">
            重置
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import type { FileInfo } from '@/services/api';

interface Props {
  visible: boolean;
  file: FileInfo | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'close': [];
}>();

// 响应式数据
const textContent = ref('');
const loadingText = ref(false);
const zoomLevel = ref(1);

// 计算属性
const fileUrl = computed(() => {
  if (!props.file) return '';
  return `http://localhost:8000${props.file.download_url}`;
});

const isTextFile = computed(() => {
  if (!props.file) return false;
  const textExtensions = ['.txt', '.md', '.json', '.xml', '.yaml', '.yml', '.log'];
  const ext = getFileExtension(props.file.filename);
  return textExtensions.includes(ext);
});

const isPdf = computed(() => {
  if (!props.file) return false;
  return getFileExtension(props.file.filename) === '.pdf';
});

// 方法
const getFileExtension = (filename: string): string => {
  return filename.toLowerCase().substring(filename.lastIndexOf('.'));
};

const getMimeType = (filename: string): string => {
  const ext = getFileExtension(filename);
  const mimeTypes: Record<string, string> = {
    '.mp3': 'audio/mpeg',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.avi': 'video/x-msvideo',
    '.mov': 'video/quicktime',
    '.mkv': 'video/x-matroska'
  };
  return mimeTypes[ext] || '';
};

const getLanguageClass = (filename: string): string => {
  const ext = getFileExtension(filename);
  const languageMap: Record<string, string> = {
    '.js': 'language-javascript',
    '.ts': 'language-typescript',
    '.py': 'language-python',
    '.java': 'language-java',
    '.cpp': 'language-cpp',
    '.c': 'language-c',
    '.html': 'language-html',
    '.css': 'language-css',
    '.json': 'language-json',
    '.xml': 'language-xml',
    '.yaml': 'language-yaml',
    '.yml': 'language-yaml'
  };
  return languageMap[ext] || 'language-text';
};

const loadTextContent = async () => {
  if (!props.file || (!isTextFile.value && props.file.file_type !== 'code')) return;
  
  loadingText.value = true;
  try {
    const response = await fetch(fileUrl.value);
    if (response.ok) {
      textContent.value = await response.text();
    } else {
      console.error('加载文件内容失败:', response.statusText);
    }
  } catch (error) {
    console.error('加载文件内容出错:', error);
  } finally {
    loadingText.value = false;
  }
};

const handleOverlayClick = () => {
  closePreview();
};

const closePreview = () => {
  emit('close');
};

const downloadFile = () => {
  if (!props.file) return;
  
  const link = document.createElement('a');
  link.href = fileUrl.value;
  link.download = props.file.filename;
  link.click();
};

const onImageLoad = () => {
  console.log('图片加载成功');
};

const onImageError = () => {
  console.error('图片加载失败');
};

const zoomIn = () => {
  if (zoomLevel.value < 3) {
    zoomLevel.value += 0.25;
  }
};

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value -= 0.25;
  }
};

const resetZoom = () => {
  zoomLevel.value = 1;
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN');
};

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (!props.visible) return;
  
  if (event.key === 'Escape') {
    closePreview();
  } else if (event.key === '+' || event.key === '=') {
    zoomIn();
  } else if (event.key === '-') {
    zoomOut();
  } else if (event.key === '0') {
    resetZoom();
  }
};

// 监听文件变化
watch(() => props.file, (newFile) => {
  if (newFile && (isTextFile.value || newFile.file_type === 'code')) {
    loadTextContent();
  }
  zoomLevel.value = 1; // 重置缩放
}, { immediate: true });

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped>
.file-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.file-preview-modal {
  background: white;
  border-radius: 8px;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.file-info .file-name {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.file-meta {
  font-size: 14px;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #f5f5f5;
}

.close-btn:hover {
  background: #ffebee;
  border-color: #ff4d4f;
}

.preview-content {
  flex: 1;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.image-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  transform: scale(v-bind(zoomLevel));
  transition: transform 0.3s ease;
}

.text-preview, .code-preview {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.text-content, .code-content {
  padding: 20px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.pdf-iframe {
  width: 80vw;
  height: 70vh;
  border: none;
}

.audio-preview, .video-preview {
  padding: 40px;
}

.audio-player {
  width: 100%;
  max-width: 500px;
}

.video-player {
  max-width: 80vw;
  max-height: 70vh;
}

.unsupported-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  text-align: center;
}

.unsupported-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.unsupported-text p {
  margin: 8px 0;
  color: #666;
}

.file-type-info {
  font-size: 14px;
  color: #999;
}

.download-btn-large {
  margin-top: 20px;
  padding: 12px 24px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.2s;
}

.download-btn-large:hover {
  background: #40a9ff;
}

.loading-state, .error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
  font-size: 16px;
}

.preview-footer {
  padding: 12px 20px;
  border-top: 1px solid #e8e8e8;
  background: #fafafa;
  display: flex;
  justify-content: center;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.zoom-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.zoom-btn:hover:not(:disabled) {
  background: #f5f5f5;
}

.zoom-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-level {
  font-size: 14px;
  color: #666;
  min-width: 50px;
  text-align: center;
}
</style>
