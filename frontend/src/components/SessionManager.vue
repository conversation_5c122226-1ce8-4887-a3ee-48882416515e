<template>
  <div class="session-manager" :class="{ collapsed: isCollapsed }">
    <!-- 侧边栏头部 -->
    <div class="session-header">
      <div class="header-title" v-if="!isCollapsed">
        <h3>会话管理</h3>
        <span class="session-count">{{ sessions.length }} 个会话</span>
      </div>
      <div class="header-actions">
        <button @click="createNewSession" class="new-session-btn" title="新建会话">
          <span class="icon">➕</span>
          <span v-if="!isCollapsed" class="btn-text">新建会话</span>
        </button>
        <button @click="toggleCollapse" class="collapse-btn" :title="isCollapsed ? '展开' : '收起'">
          <span class="icon">{{ isCollapsed ? '▶️' : '◀️' }}</span>
        </button>
      </div>
    </div>

    <!-- 会话列表 -->
    <div class="session-list" v-if="!isCollapsed">
      <div class="session-search">
        <input 
          v-model="searchQuery" 
          placeholder="搜索会话..." 
          class="search-input"
        />
      </div>
      
      <div class="sessions-container">
        <div
          v-for="(session, index) in filteredSessions"
          :key="session.id"
          class="session-item"
          :class="{
            active: session.id === currentSessionId,
            editing: editingSessionId === session.id,
            dragging: draggingSessionId === session.id
          }"
          @click="selectSession(session.id)"
          @mousedown="startDrag($event, session.id, index)"
          draggable="true"
          @dragstart="onDragStart($event, session.id, index)"
          @dragover="onDragOver($event, index)"
          @drop="onDrop($event, index)"
          @dragend="onDragEnd"
        >
          <!-- 会话图标 -->
          <div class="session-icon">
            <span class="icon">💬</span>
          </div>
          
          <!-- 会话信息 -->
          <div class="session-info" v-if="editingSessionId !== session.id">
            <div class="session-title">{{ session.title || '新对话' }}</div>
            <div class="session-meta">
              <span class="message-count">{{ session.messageCount }} 条消息</span>
              <span class="last-time">{{ formatTime(session.lastActiveTime) }}</span>
            </div>
          </div>
          
          <!-- 编辑模式 -->
          <div class="session-edit" v-else>
            <input 
              v-model="editingTitle"
              @keyup.enter="saveSessionTitle(session.id)"
              @keyup.escape="cancelEdit"
              @blur="saveSessionTitle(session.id)"
              class="edit-input"
              ref="editInput"
            />
          </div>
          
          <!-- 操作按钮 -->
          <div class="session-actions">
            <button 
              @click.stop="startEdit(session.id, session.title)"
              class="action-btn edit-btn"
              title="重命名"
            >
              <span class="icon">✏️</span>
            </button>
            <button 
              @click.stop="deleteSession(session.id)"
              class="action-btn delete-btn"
              title="删除"
            >
              <span class="icon">🗑️</span>
            </button>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="filteredSessions.length === 0" class="empty-state">
          <div class="empty-icon">💭</div>
          <div class="empty-text">
            {{ searchQuery ? '没有找到匹配的会话' : '还没有会话，点击上方按钮创建新会话' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 收起状态的会话指示器 -->
    <div v-else class="collapsed-indicator">
      <div 
        v-for="session in sessions.slice(0, 5)" 
        :key="session.id"
        class="session-dot"
        :class="{ active: session.id === currentSessionId }"
        @click="selectSession(session.id)"
        :title="session.title || '新对话'"
      ></div>
      <div v-if="sessions.length > 5" class="more-sessions">
        +{{ sessions.length - 5 }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue';

interface Session {
  id: string;
  title: string;
  messageCount: number;
  lastActiveTime: Date;
  createdAt: Date;
}

interface Props {
  sessions: Session[];
  currentSessionId: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'session-select': [sessionId: string];
  'session-create': [];
  'session-delete': [sessionId: string];
  'session-rename': [sessionId: string, newTitle: string];
  'session-reorder': [sessionIds: string[]];
}>();

// 响应式数据
const isCollapsed = ref(false);
const searchQuery = ref('');
const editingSessionId = ref<string | null>(null);
const editingTitle = ref('');
const editInput = ref<HTMLInputElement>();

// 拖拽相关状态
const draggingSessionId = ref<string | null>(null);
const dragStartIndex = ref<number>(-1);
const dragOverIndex = ref<number>(-1);

// 计算属性
const filteredSessions = computed(() => {
  if (!searchQuery.value) {
    return props.sessions.sort((a, b) => 
      new Date(b.lastActiveTime).getTime() - new Date(a.lastActiveTime).getTime()
    );
  }
  
  return props.sessions.filter(session =>
    session.title.toLowerCase().includes(searchQuery.value.toLowerCase())
  ).sort((a, b) => 
    new Date(b.lastActiveTime).getTime() - new Date(a.lastActiveTime).getTime()
  );
});

// 方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

const createNewSession = () => {
  emit('session-create');
};

const selectSession = (sessionId: string) => {
  if (editingSessionId.value) return; // 编辑模式下不允许切换
  emit('session-select', sessionId);
};

const deleteSession = (sessionId: string) => {
  if (confirm('确定要删除这个会话吗？删除后无法恢复。')) {
    emit('session-delete', sessionId);
  }
};

const startEdit = async (sessionId: string, currentTitle: string) => {
  editingSessionId.value = sessionId;
  editingTitle.value = currentTitle || '新对话';
  
  await nextTick();
  if (editInput.value) {
    editInput.value.focus();
    editInput.value.select();
  }
};

const saveSessionTitle = (sessionId: string) => {
  if (editingTitle.value.trim() && editingTitle.value !== props.sessions.find(s => s.id === sessionId)?.title) {
    emit('session-rename', sessionId, editingTitle.value.trim());
  }
  cancelEdit();
};

const cancelEdit = () => {
  editingSessionId.value = null;
  editingTitle.value = '';
};

const formatTime = (time: Date) => {
  const now = new Date();
  const diff = now.getTime() - new Date(time).getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;

  return new Date(time).toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  });
};

// 拖拽相关方法
const startDrag = (event: MouseEvent, sessionId: string, index: number) => {
  // 防止在编辑模式下拖拽
  if (editingSessionId.value) {
    event.preventDefault();
    return;
  }
};

const onDragStart = (event: DragEvent, sessionId: string, index: number) => {
  draggingSessionId.value = sessionId;
  dragStartIndex.value = index;

  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', sessionId);
  }
};

const onDragOver = (event: DragEvent, index: number) => {
  event.preventDefault();
  dragOverIndex.value = index;

  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move';
  }
};

const onDrop = (event: DragEvent, dropIndex: number) => {
  event.preventDefault();

  if (dragStartIndex.value === -1 || dragStartIndex.value === dropIndex) {
    return;
  }

  // 重新排序会话
  const newSessions = [...filteredSessions.value];
  const draggedSession = newSessions.splice(dragStartIndex.value, 1)[0];
  newSessions.splice(dropIndex, 0, draggedSession);

  // 发出重新排序事件
  const newOrder = newSessions.map(session => session.id);
  emit('session-reorder', newOrder);
};

const onDragEnd = () => {
  draggingSessionId.value = null;
  dragStartIndex.value = -1;
  dragOverIndex.value = -1;
};

// 监听编辑状态变化
watch(editingSessionId, (newVal) => {
  if (!newVal) {
    editingTitle.value = '';
  }
});
</script>

<style scoped>
.session-manager {
  width: 280px;
  height: 100vh;
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
}

.session-manager.collapsed {
  width: 60px;
}

.session-header {
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
  background: white;
}

.header-title h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.session-count {
  font-size: 12px;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.new-session-btn, .collapse-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  transition: all 0.2s;
}

.new-session-btn {
  flex: 1;
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.new-session-btn:hover {
  background: #40a9ff;
}

.collapse-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

.session-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.session-search {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}

.search-input:focus {
  border-color: #1890ff;
}

.sessions-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.session-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.session-item:hover {
  background: #f0f0f0;
}

.session-item.active {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.session-item.editing {
  background: #fff7e6;
  border: 1px solid #ffd666;
}

.session-item.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.session-item:hover {
  cursor: grab;
}

.session-item.dragging {
  cursor: grabbing;
}

.session-icon {
  margin-right: 12px;
  font-size: 16px;
}

.session-info {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.session-edit {
  flex: 1;
  margin-right: 8px;
}

.edit-input {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid #1890ff;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}

.session-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.session-item:hover .session-actions {
  opacity: 1;
}

.action-btn {
  padding: 4px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  font-size: 12px;
  transition: background 0.2s;
}

.action-btn:hover {
  background: rgba(0, 0, 0, 0.1);
}

.delete-btn:hover {
  background: #ffebee;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
  line-height: 1.5;
}

.collapsed-indicator {
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.session-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ddd;
  cursor: pointer;
  transition: all 0.2s;
}

.session-dot.active {
  background: #1890ff;
  transform: scale(1.2);
}

.session-dot:hover {
  background: #40a9ff;
}

.more-sessions {
  font-size: 10px;
  color: #666;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .session-manager {
    width: 100%;
    height: auto;
    max-height: 300px;
    border-right: none;
    border-bottom: 1px solid #e8e8e8;
  }
  
  .session-manager.collapsed {
    height: 60px;
    width: 100%;
  }
  
  .sessions-container {
    max-height: 200px;
  }
}
</style>
