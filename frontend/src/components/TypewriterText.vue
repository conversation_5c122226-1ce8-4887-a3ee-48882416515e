<template>
  <div class="typewriter-container">
    <span class="typewriter-text">{{ displayText }}</span>
    <span v-if="showCursor && !isComplete" class="typewriter-cursor">|</span>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue';

interface Props {
  text: string;
  speed?: number; // 打字速度（毫秒）
  showCursor?: boolean;
  autoStart?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  speed: 30,
  showCursor: true,
  autoStart: true,
});

const emit = defineEmits<{
  complete: [];
  progress: [progress: number];
}>();

const displayText = ref('');
const isComplete = ref(false);
const currentIndex = ref(0);
let typewriterTimer: number | null = null;

const startTypewriter = () => {
  if (typewriterTimer) {
    clearInterval(typewriterTimer);
  }
  
  displayText.value = '';
  currentIndex.value = 0;
  isComplete.value = false;

  if (!props.text) {
    isComplete.value = true;
    emit('complete');
    return;
  }

  typewriterTimer = setInterval(() => {
    if (currentIndex.value < props.text.length) {
      displayText.value += props.text[currentIndex.value];
      currentIndex.value++;
      
      const progress = (currentIndex.value / props.text.length) * 100;
      emit('progress', progress);
    } else {
      if (typewriterTimer) {
        clearInterval(typewriterTimer);
        typewriterTimer = null;
      }
      isComplete.value = true;
      emit('complete');
    }
  }, props.speed);
};

const stopTypewriter = () => {
  if (typewriterTimer) {
    clearInterval(typewriterTimer);
    typewriterTimer = null;
  }
};

const completeImmediately = () => {
  stopTypewriter();
  displayText.value = props.text;
  currentIndex.value = props.text.length;
  isComplete.value = true;
  emit('complete');
};

// 监听文本变化
watch(() => props.text, (newText, oldText) => {
  if (newText !== oldText) {
    if (props.autoStart) {
      startTypewriter();
    }
  }
}, { immediate: props.autoStart });

// 暴露方法给父组件
defineExpose({
  start: startTypewriter,
  stop: stopTypewriter,
  complete: completeImmediately,
  isComplete: () => isComplete.value,
});

onMounted(() => {
  if (props.autoStart && props.text) {
    startTypewriter();
  }
});

onUnmounted(() => {
  stopTypewriter();
});
</script>

<style scoped>
.typewriter-container {
  display: inline-block;
}

.typewriter-text {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.typewriter-cursor {
  animation: blink 1s infinite;
  font-weight: bold;
  color: #1890ff;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
</style>
