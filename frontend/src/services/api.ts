import axios from 'axios';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string;
}

export interface ChatRequest {
  message: string;
  thread_id?: string;
  stream?: boolean;
}

export interface ChatResponse {
  message: string;
  thread_id: string;
  timestamp: string;
}

export class ChatAPI {
  private baseURL = 'http://localhost:8000';
  private client = axios.create({
    baseURL: this.baseURL,
    timeout: 30000,
  });

  // 发送聊天消息
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    const response = await this.client.post('/api/chat', request);
    return response.data;
  }

  // 流式聊天
  async sendStreamMessage(request: ChatRequest): Promise<ReadableStream> {
    const response = await fetch(`${this.baseURL}/api/chat/stream`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    });
    
    if (!response.body) {
      throw new Error('No response body');
    }
    
    return response.body;
  }

  // WebSocket连接
  connectWebSocket(threadId: string): WebSocket {
    return new WebSocket(`ws://localhost:8000/ws/${threadId}`);
  }

  // 获取会话历史
  async getSessionHistory(threadId: string): Promise<ChatMessage[]> {
    const response = await this.client.get(`/api/sessions/${threadId}/history`);
    return response.data.history || [];
  }

  // 获取可用工具
  async getAvailableTools(): Promise<any[]> {
    const response = await this.client.get('/api/tools');
    return response.data.tools || [];
  }
}

export const chatAPI = new ChatAPI();
