"""
Web API接口实现
使用FastAPI提供REST API和WebSocket接口
"""

import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# 导入核心组件
from core.agent_core import agent_core
from services.config_manager import config_manager
from services.error_handler import error_handler, CustomError, ErrorCode


# Pydantic模型定义
class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str = Field(..., description="消息角色: user, assistant, system")
    content: str = Field(..., description="消息内容")
    timestamp: Optional[datetime] = Field(default_factory=datetime.now, description="时间戳")


class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., description="用户消息")
    thread_id: Optional[str] = Field(None, description="会话ID")
    stream: bool = Field(False, description="是否流式响应")


class ChatResponse(BaseModel):
    """聊天响应模型"""
    message: str = Field(..., description="AI回复")
    thread_id: str = Field(..., description="会话ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


class SessionInfo(BaseModel):
    """会话信息模型"""
    thread_id: str = Field(..., description="会话ID")
    created_at: datetime = Field(..., description="创建时间")
    message_count: int = Field(..., description="消息数量")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    version: str = Field(..., description="版本信息")


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_threads: Dict[str, str] = {}  # connection_id -> thread_id
    
    async def connect(self, websocket: WebSocket, connection_id: str, thread_id: Optional[str] = None):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        if thread_id:
            self.connection_threads[connection_id] = thread_id
        print(f"WebSocket连接已建立: {connection_id}")
    
    def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        if connection_id in self.connection_threads:
            del self.connection_threads[connection_id]
        print(f"WebSocket连接已断开: {connection_id}")
    
    async def send_message(self, connection_id: str, message: dict):
        """发送消息到指定连接"""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            try:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                print(f"发送WebSocket消息失败: {e}")
                self.disconnect(connection_id)
    
    async def broadcast(self, message: dict):
        """广播消息到所有连接"""
        disconnected = []
        for connection_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                print(f"广播消息失败 {connection_id}: {e}")
                disconnected.append(connection_id)
        
        # 清理断开的连接
        for connection_id in disconnected:
            self.disconnect(connection_id)


# 创建FastAPI应用
def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    
    # 获取Web配置
    web_config = config_manager.get_web_config()
    
    app = FastAPI(
        title="LangGraph Agent Web API",
        description="基于LangGraph的智能助手Web API接口",
        version="2.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=web_config.get('cors_origins', ['*']),
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 创建WebSocket管理器
    websocket_manager = WebSocketManager()
    
    # 健康检查端点
    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        """健康检查"""
        return HealthResponse(
            status="healthy",
            version="2.0.0"
        )
    
    # 聊天API端点
    @app.post("/api/chat", response_model=ChatResponse)
    async def chat_endpoint(request: ChatRequest):
        """聊天API端点"""
        try:
            # 确保agent已初始化
            await agent_core.initialize()
            
            # 生成thread_id（如果未提供）
            thread_id = request.thread_id or f"web_user_{uuid.uuid4().hex[:8]}"
            
            # 处理消息
            response = await agent_core.process_message(
                message=request.message,
                thread_id=thread_id
            )

            return ChatResponse(
                message=response["response"],
                thread_id=thread_id
            )
            
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "chat", "request": request.dict()})
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # 流式聊天API端点
    @app.post("/api/chat/stream")
    async def chat_stream_endpoint(request: ChatRequest):
        """流式聊天API端点"""
        try:
            # 确保agent已初始化
            await agent_core.initialize()
            
            # 生成thread_id（如果未提供）
            thread_id = request.thread_id or f"web_user_{uuid.uuid4().hex[:8]}"
            
            async def generate():
                try:
                    async for chunk in agent_core.stream_message(
                        message=request.message,
                        thread_id=thread_id
                    ):
                        yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                except Exception as e:
                    error_data = {
                        "error": str(e),
                        "thread_id": thread_id,
                        "timestamp": datetime.now().isoformat()
                    }
                    yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
            
            from fastapi.responses import StreamingResponse
            return StreamingResponse(
                generate(),
                media_type="text/plain",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Content-Type": "text/event-stream"
                }
            )
            
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "chat_stream", "request": request.dict()})
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # 获取会话历史
    @app.get("/api/sessions/{thread_id}/history")
    async def get_session_history(thread_id: str):
        """获取会话历史"""
        try:
            # 确保agent已初始化
            await agent_core.initialize()
            
            history = await agent_core.get_session_history(thread_id)
            return {"thread_id": thread_id, "history": history}
            
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "session_history", "thread_id": thread_id})
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # 获取可用工具列表
    @app.get("/api/tools")
    async def get_available_tools():
        """获取可用工具列表"""
        try:
            # 确保agent已初始化
            await agent_core.initialize()
            
            tools = agent_core.get_available_tools()
            return {"tools": tools}
            
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "tools"})
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # WebSocket端点
    @app.websocket("/ws/{thread_id}")
    async def websocket_endpoint(websocket: WebSocket, thread_id: str):
        """WebSocket聊天端点"""
        connection_id = f"ws_{uuid.uuid4().hex[:8]}"
        
        try:
            await websocket_manager.connect(websocket, connection_id, thread_id)
            
            # 确保agent已初始化
            await agent_core.initialize()
            
            # 发送连接确认
            await websocket_manager.send_message(connection_id, {
                "type": "connection",
                "status": "connected",
                "thread_id": thread_id,
                "connection_id": connection_id
            })
            
            while True:
                # 接收消息
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                if message_data.get("type") == "chat":
                    user_message = message_data.get("message", "")
                    
                    # 发送用户消息确认
                    await websocket_manager.send_message(connection_id, {
                        "type": "user_message",
                        "message": user_message,
                        "timestamp": datetime.now().isoformat()
                    })
                    
                    # 流式处理并发送AI回复
                    async for chunk in agent_core.stream_message(user_message, thread_id):
                        await websocket_manager.send_message(connection_id, {
                            "type": "ai_chunk",
                            "chunk": chunk,
                            "timestamp": datetime.now().isoformat()
                        })
                    
                    # 发送完成信号
                    await websocket_manager.send_message(connection_id, {
                        "type": "ai_complete",
                        "timestamp": datetime.now().isoformat()
                    })
                
        except WebSocketDisconnect:
            websocket_manager.disconnect(connection_id)
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "websocket", "connection_id": connection_id})
            await websocket_manager.send_message(connection_id, {
                "type": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            websocket_manager.disconnect(connection_id)
    
    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    # 获取Web配置
    web_config = config_manager.get_web_config()
    
    uvicorn.run(
        "interfaces.web_api:app",
        host=web_config.get('host', '0.0.0.0'),
        port=web_config.get('port', 8000),
        reload=web_config.get('debug', False),
        log_level="info"
    )
